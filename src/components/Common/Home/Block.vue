<template>
    <section class="home-block">
        <div class="header">
            <h2 class="title">{{ title }}</h2>
            <p class="more" v-if="more" @click="onClick">
                <span>{{ more }}</span>
                <span class="icon-arrow" />
            </p>
        </div>
        <div class="content">
            <slot />
        </div>
    </section>
</template>

<script setup>
import { toRefs } from 'vue'

const props = defineProps({
    title: {
        type: String,
        required: true
    },
    more: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['click'])

const { title, more } = toRefs(props)

const onClick = () => {
    emit('click')
}
</script>

<style lang="less" scoped>
.home-block {
    margin: 10px 0;
    background: #fff;
    overflow: hidden;
    transition: box-shadow 0.3s ease;
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 20px 10px;

        .title {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            line-height: 1.4;
            margin: 0;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                left: -12px;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 16px;
                background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                border-radius: 2px;
            }
        }

        .more {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 400;
            color: #666;
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 20px;
            background: #f8f9fa;
            border: 1px solid transparent;
            transition: all 0.3s ease;

            &:hover {
                color: #ff6b6b;
                background: #fff;
                border-color: #ff6b6b;
                transform: translateY(-1px);
            }

            .icon-arrow {
                margin-left: 6px;
                width: 12px;
                height: 12px;
                background-image: url('./assets/arrow.png');
                background-repeat: no-repeat;
                background-size: contain;
                background-position: center;
                transition: transform 0.3s ease;
                opacity: 0.7;
            }

            &:hover .icon-arrow {
                transform: translateX(3px);
                opacity: 1;
            }
        }
    }

    .content {
        padding: 10px;
        background: #fff;
    }
}
</style>
