<template>
  <div class="skeleton-test">
    <h2>骨架屏优化测试页面</h2>

    <div class="test-controls">
      <button @click="toggleBanner" class="test-btn">
        {{ showBannerSkeleton ? '显示 Banner' : '显示 Banner 骨架屏' }}
      </button>
      <button @click="toggleGridMenu" class="test-btn">
        {{ showGridMenuSkeleton ? '显示网格菜单' : '显示网格菜单骨架屏' }}
      </button>
      <button @click="toggleHorizontalScroll" class="test-btn">
        {{ showHorizontalScrollSkeleton ? '显示水平滚动' : '显示水平滚动骨架屏' }}
      </button>
    </div>

    <!-- Banner 测试 -->
    <div class="test-section">
      <h3>Banner 组件测试</h3>
      <div class="banner-container">
        <BannerSkeleton v-if="showBannerSkeleton" />
        <GoodsImageSwiper v-else :media-list="bannerList" mode="banner"
          paginationType="fraction" :autoplay="true" :loop="true" />
      </div>
    </div>

    <!-- GridMenu 测试 -->
    <div class="test-section">
      <h3>网格菜单测试</h3>
      <div class="grid-menu-container">
        <GridMenuSkeleton v-if="showGridMenuSkeleton" />
        <GridMenu v-else :items="gridMenuItems" :columns="5"
          :show-more="true" :max-items="10" />
      </div>
    </div>

    <!-- HorizontalScroll 测试 -->
    <div class="test-section">
      <h3>水平滚动测试</h3>
      <div class="horizontal-scroll-container" :style="{ minHeight: '180px' }">
        <HorizontalScrollSkeleton v-if="showHorizontalScrollSkeleton" :skeleton-count="5" />
        <div v-else class="horizontal-scroll-wrapper">
          <div class="goods-item" v-for="item in horizontalScrollItems" :key="item.goodsId">
            <GoodsCard :goods-info="item" />
          </div>
        </div>
      </div>
    </div>

    <div class="test-info">
      <h3>测试说明</h3>
      <ul>
        <li>点击按钮可以在骨架屏和实际内容之间切换</li>
        <li>观察切换过程中是否有明显的闪烁或跳动</li>
        <li>Banner 组件现在使用图片自然高度，骨架屏高度已调整为更合理的值</li>
        <li>检查骨架屏的尺寸是否与实际内容匹配良好</li>
        <li>验证响应式布局在不同屏幕尺寸下的表现</li>
        <li>测试不同高度的图片在 Banner 中的自适应效果</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import BannerSkeleton from '@components/Common/Home/Skeleton/BannerSkeleton.vue'
import GridMenuSkeleton from '@components/Common/Home/Skeleton/GridMenuSkeleton.vue'
import HorizontalScrollSkeleton from '@components/Common/Home/Skeleton/HorizontalScrollSkeleton.vue'
import GoodsImageSwiper from '@components/Common/GoodsImageSwiper.vue'
import GridMenu from '@components/Common/Home/GridMenu.vue'
import GoodsCard from '@components/Common/Home/GoodsCard.vue'

const showBannerSkeleton = ref(true)
const showGridMenuSkeleton = ref(true)
const showHorizontalScrollSkeleton = ref(true)

// 模拟 Banner 数据 - 使用不同尺寸的图片测试自适应高度
const bannerList = ref([
  {
    type: 'image',
    url: 'https://picsum.photos/800/300?random=1',
    alt: '测试横幅1 - 较高图片'
  },
  {
    type: 'image',
    url: 'https://picsum.photos/800/200?random=2',
    alt: '测试横幅2 - 中等高度'
  },
  {
    type: 'image',
    url: 'https://picsum.photos/800/250?random=3',
    alt: '测试横幅3 - 标准高度'
  }
])

// 模拟网格菜单数据
const gridMenuItems = ref([
  { title: '菜单1', subtitle: '副标题1', icon: 'https://picsum.photos/40/40?random=11' },
  { title: '菜单2', subtitle: '副标题2', icon: 'https://picsum.photos/40/40?random=12' },
  { title: '菜单3', subtitle: '副标题3', icon: 'https://picsum.photos/40/40?random=13' },
  { title: '菜单4', subtitle: '副标题4', icon: 'https://picsum.photos/40/40?random=14' }
])

// 模拟水平滚动商品数据
const horizontalScrollItems = ref([
  {
    goodsId: 1,
    name: '测试商品1 - 这是一个用于测试水平滚动的商品名称',
    price: '99.99',
    sales: 123,
    image: 'https://picsum.photos/200/200?random=21',
    spec: '规格1'
  },
  {
    goodsId: 2,
    name: '测试商品2 - 这是一个用于测试水平滚动的商品名称',
    price: '199.99',
    sales: 456,
    image: 'https://picsum.photos/200/200?random=22',
    spec: '规格2'
  },
  {
    goodsId: 3,
    name: '测试商品3 - 这是一个用于测试水平滚动的商品名称',
    price: '299.99',
    sales: 789,
    image: 'https://picsum.photos/200/200?random=23',
    spec: '规格3'
  },
  {
    goodsId: 4,
    name: '测试商品4 - 这是一个用于测试水平滚动的商品名称',
    price: '399.99',
    sales: 321,
    image: 'https://picsum.photos/200/200?random=24',
    spec: '规格4'
  },
  {
    goodsId: 5,
    name: '测试商品5 - 这是一个用于测试水平滚动的商品名称',
    price: '499.99',
    sales: 654,
    image: 'https://picsum.photos/200/200?random=25',
    spec: '规格5'
  }
])

const toggleBanner = () => {
  showBannerSkeleton.value = !showBannerSkeleton.value
}

const toggleGridMenu = () => {
  showGridMenuSkeleton.value = !showGridMenuSkeleton.value
}

const toggleHorizontalScroll = () => {
  showHorizontalScrollSkeleton.value = !showHorizontalScrollSkeleton.value
}
</script>

<style scoped lang="less">
@import '@/assets/css/design-system.less';

.skeleton-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
  }

  h3 {
    margin: 20px 0 10px 0;
    color: #666;
    font-size: 16px;
  }

  .test-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-bottom: 30px;
    flex-wrap: wrap;

    .test-btn {
      padding: 8px 16px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      white-space: nowrap;

      &:hover {
        background: #0056b3;
      }
    }
  }

  .test-section {
    margin-bottom: 40px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    background: #fff;
  }

  // 与 IndexView.vue 中的样式保持一致
  .banner-container {
    margin: @radius-8 @radius-12;
    border-radius: @radius-12;
    overflow: hidden;
  }

  .grid-menu-container {
    background: @bg-color-white;
    border-radius: @radius-12;
    margin: @radius-8 @radius-12;
  }

  .horizontal-scroll-container {
    position: relative;
    min-height: 180px;
    display: flex;
    align-items: center;

    .horizontal-scroll-wrapper {
      display: flex;
      gap: @radius-12;
      overflow-x: auto;
      padding-bottom: @radius-8;
      scroll-behavior: smooth;
      width: 100%;
      .no-scrollbar();

      .goods-item {
        flex: 0 0 160px;
        cursor: pointer;

        &:last-child {
          margin-right: @radius-12;
        }
      }
    }
  }

  .test-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    ul {
      margin: 10px 0;
      padding-left: 20px;

      li {
        margin: 8px 0;
        font-size: 14px;
        color: #666;
        line-height: 1.5;
      }
    }
  }
}

// 隐藏滚动条的混合类
.no-scrollbar() {
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
